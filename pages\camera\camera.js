// camera.js
Page({
  data: {
    cameraContext: null,
    devicePosition: 'back',
    flash: 'off'
  },

  onLoad() {
    this.setData({
      cameraContext: wx.createCameraContext()
    })
  },

  // 切换前后摄像头
  switchCamera() {
    this.setData({
      devicePosition: this.data.devicePosition === 'back' ? 'front' : 'back'
    })
  },

  // 切换闪光灯
  switchFlash() {
    this.setData({
      flash: this.data.flash === 'off' ? 'on' : 'off'
    })
  },

  // 拍照
  takePhoto() {
    const camera = this.data.cameraContext
    camera.takePhoto({
      quality: 'high',
      success: (res) => {
        const tempImagePath = res.tempImagePath
        wx.showToast({
          title: '拍照成功',
          icon: 'success'
        })
        // 这里可以添加保存照片或上传照片的逻辑
      },
      fail: (err) => {
        console.error(err)
        wx.showToast({
          title: '拍照失败',
          icon: 'error'
        })
      }
    })
  }
})
// 引入请求工具
const { get, post, put, del } = require('../utils/request_java_style.js');

// API基础URL
const BASE_URL = 'http://36.133.208.76:8088'; // 替换为您的实际API基础URL

// create: (data) => post(`${BASE_URL}/content`, data),
// getList: (params) => get(`${BASE_URL}/content/list`, params),

// API接口封装
const api = {
  getList: (data) => post(`${BASE_URL}/api/open/village/infoReport/queryReport`, data),
  report: (data) => post(`${BASE_URL}/api/open/village/infoReport/report`, data),
};

module.exports = api;
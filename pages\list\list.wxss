/* list.wxss */
.containers {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f6f6f6;
}

.list-header {
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.list-title {
  font-size: 34rpx;
  font-weight: bold;
  color: #333;
}

.list-content {
  flex: 1;
  padding: 20rpx;
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-main {
  flex: 1;
  margin-right: 20rpx;
}

.item-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.item-time {
  font-size: 26rpx;
  color: #999;
}

.item-extra {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
}

.status-0 {
  background-color: #ff9800; /* 待处理 - 橙色 */
}

.status-1 {
  background-color: #2196f3; /* 处理中 - 蓝色 */
}

.status-2 {
  background-color: #4caf50; /* 已完成 - 绿色 */
}

.score-tag {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.score-value {
  font-size: 36rpx;
  color: #ff6b00;
  font-weight: bold;
}

.score-label {
  font-size: 24rpx;
  color: #999;
}

.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

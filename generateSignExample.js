const CryptoJS = require('./utils/crypto-js.min.js');

/**
 * 生成API请求签名示例
 */

// 配置信息
const config = {
  thirdKey: '60820f04-2eb',     // 替换为您的实际thirdKey
  thirdSecret: 'A4AC796044AF1B441A96'  // 替换为您的实际thirdSecret
};

/**
 * 生成签名
 * @param {string} uri - 请求路径
 * @param {object|string} params - 请求参数
 * @returns {object} 包含签名和请求时间的对象
 */
function generateSign(uri, params) {
  const requestTime = Date.now().toString();

  // 将params转为字符串
  let paramsStr = '';
  if (typeof params === 'object') {
    paramsStr = JSON.stringify(params);
  } else if (typeof params === 'string') {
    paramsStr = params;
  }

  // 第一步：使用stringBuffer作为密钥，对thirdSecret进行HMAC-SHA256加密
  const stringBuffer = `${config.thirdKey}|${requestTime}|${uri}`;
  // 在CryptoJS中，HmacSHA256的参数顺序是(消息, 密钥)
  const kDate = CryptoJS.HmacSHA256(config.thirdSecret, stringBuffer);

  // 第二步：计算params的SHA256哈希
  const messageType = CryptoJS.SHA256(paramsStr);

  // 第三步：使用kDate作为密钥，对messageType进行HMAC-SHA256加密
  const signature = CryptoJS.HmacSHA256(messageType, kDate).toString(CryptoJS.enc.Hex);

  return {
    signature,
    requestTime
  };
}

/**
 * 示例：生成请求头
 * @param {string} uri - 请求路径
 * @param {object} data - 请求参数
 * @returns {object} 请求头对象
 */
function generateRequestHeader(uri, data) {
  // 生成签名和时间戳
  const { signature, requestTime } = generateSign(uri, data);

  // 构建请求头
  return {
    'Accept-ThirdKey': config.thirdKey,
    'Accept-Time': requestTime,
    'Accept-Sign': signature,
    'Content-Type': 'application/json'
  };
}

// 使用示例
function example() {
  // 请求路径
  const uri = 'api/open/village/infoReport/repparams';
  
  // 请求参数
  const params = {
    name: '测试村庄',
    code: '123456',
    type: 1
  };
  
  // 生成请求头
  const headers = generateRequestHeader(uri, params);
  
  console.log('请求头:', headers);
  
  // 在实际请求中使用
  // 微信小程序示例
  /*
  wx.request({
    url: 'http://api.example.com/' + uri,
    method: 'POST',
    data: params,
    header: headers,
    success: (res) => {
      console.log('请求成功:', res.data);
    },
    fail: (err) => {
      console.error('请求失败:', err);
    }
  });
  */
  
  // 普通JavaScript/Node.js示例（使用fetch）
  /*
  fetch('http://api.example.com/' + uri, {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(params)
  })
  .then(response => response.json())
  .then(data => console.log('请求成功:', data))
  .catch(error => console.error('请求失败:', error));
  */
}

// 运行示例
example();

// 导出函数供其他模块使用
module.exports = {
  generateSign,
  generateRequestHeader
};

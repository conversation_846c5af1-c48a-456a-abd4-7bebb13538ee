.profile-container {
  min-height: 100vh;
  background-color: #f6f6f6;
}

.user-info-section {
  background-color: #fff;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 20rpx;
}

.avatar-container {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.avatar {
  width: 100%;
  height: 100%;
}

.user-name {
  font-size: 32rpx;
  color: #333;
}

.login-btn {
  font-size: 32rpx;
  color: #0f85cf;
  background: none;
  border: none;
  padding: 0;
  line-height: 1.5;
}

.login-btn::after {
  border: none;
}

.menu-list {
  background-color: #fff;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-item-left text {
  font-size: 30rpx;
  color: #333;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.5;
}

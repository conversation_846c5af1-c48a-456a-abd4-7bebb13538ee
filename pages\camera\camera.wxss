.camera-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  background-color: #000;
}

.controls {
  position: absolute;
  bottom: 50rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 30rpx;
}

.control-btn {
  background-color: rgba(255, 255, 255, 0.3);
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
}

.control-btn text {
  color: #fff;
  font-size: 28rpx;
}

.shoot-btn {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

.inner-circle {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #fff;
}

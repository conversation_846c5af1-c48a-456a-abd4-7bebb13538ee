const CryptoJS = require('./crypto-js.min.js');

// 配置信息
const config = {
  baseUrl: 'http://36.133.208.76:8088', // 替换为您的实际API基础URL
  thirdKey: '60820f04-2eb',
  thirdSecret: 'A4AC796044AF1B441A96'
};

/**
 * 生成签名
 * @param {string} uri - 请求路径
 * @param {object|string} params - 请求参数
 * @returns {object} 包含签名和请求时间的对象
 */
function generateSign(uri, params) {
  const requestTime = Date.now().toString();

  // 将params转为字符串
  let paramsStr = '';
  if (typeof params === 'object') {
    paramsStr = JSON.stringify(params);
  } else if (typeof params === 'string') {
    paramsStr = params;
  }
  console.log(paramsStr);


  // 第一步：使用stringBuffer作为密钥，对thirdSecret进行HMAC-SHA256加密
  // Java代码：
  // String stringBuffer = thirdKey + "|" + requestTime + "|" + uri;
  // HmacUtils hmac = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, stringBuffer);
  // byte[] kDate = hmac.hmac(thirdSecret);
  const stringBuffer = `${config.thirdKey}|${requestTime}|${uri}`;
  console.log('stringBuffer:', stringBuffer);
  // 在CryptoJS中，HmacSHA256的参数顺序是(消息, 密钥)
  const kDate = CryptoJS.HmacSHA256(config.thirdSecret, stringBuffer);
  console.log('kDate:', kDate);

  // 第二步：计算params的SHA256哈希
  // Java代码：
  // byte[] messageType = DigestUtils.sha256(params);
  const messageType = CryptoJS.SHA256(paramsStr);
  console.log('messageType:', messageType);

  // 第三步：使用kDate作为密钥，对messageType进行HMAC-SHA256加密
  // Java代码：
  // hmac = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, kDate);
  // return hmac.hmacHex(messageType);
  // 在CryptoJS中，HmacSHA256的参数顺序是(消息, 密钥)
  const signature = CryptoJS.HmacSHA256(messageType, kDate).toString(CryptoJS.enc.Hex);
  console.log('signature:', signature);

  return {
    signature,
    requestTime
  };
}

/**
 * 请求函数
 * @param {object} options - 请求选项
 * @returns {Promise} 请求Promise
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const { url, method = 'GET', data = {}, header = {} } = options;

    // 获取相对路径URI
    const uri = url.replace(config.baseUrl, '');
    console.log(uri, data);

    // 生成签名和时间戳
    const { signature, requestTime } = generateSign(uri, data);

    // 构建请求头
    const requestHeader = {
      'Accept-ThirdKey': config.thirdKey,
      'Accept-Time': requestTime,
      'Accept-Sign': signature,
      'Content-Type': 'application/json',
      ...header
    };

    // 发起请求
    wx.request({
      url: url.startsWith('http') ? url : config.baseUrl + url,
      method,
      data,
      header: requestHeader,
      success: (res) => {
        // 可以在这里统一处理响应
        if (res.statusCode >= 200 && res.statusCode < 300) {
          resolve(res.data);
        } else {
          // 处理错误状态码
          reject({
            code: res.statusCode,
            message: res.data.message || '请求失败',
            data: res.data
          });
        }
      },
      fail: (err) => {
        // 处理请求失败
        reject({
          code: -1,
          message: err.errMsg || '网络请求失败',
          data: err
        });
      }
    });
  });
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function get(url, data = {}, header = {}) {
  return request({
    url,
    method: 'GET',
    data,
    header
  });
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function post(url, data = {}, header = {}) {
  return request({
    url,
    method: 'POST',
    data,
    header
  });
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function put(url, data = {}, header = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    header
  });
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function del(url, data = {}, header = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    header
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  del
};
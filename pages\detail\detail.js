// pages/detail.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    detailData: null,
    type: '',
    replyImages: [],
    reportImages: [],
    categoryData: {
      "bizTypes": [
        {
          "code": 1,
          "subBizTypes": [
            {
              "code": 1,
              "name": "疫情"
            },
            {
              "code": 2,
              "name": "农灾"
            },
            {
              "code": 3,
              "name": "地震"
            },
            {
              "code": 25,
              "name": "火灾"
            },
            {
              "code": 4,
              "name": "洪水"
            },
            {
              "code": 5,
              "name": "其他"
            }
          ],
          "name": "疫情灾情"
        },
        {
          "code": 2,
          "subBizTypes": [
            {
              "code": 6,
              "name": "街道环境"
            },
            {
              "code": 7,
              "name": "家乡美景"
            },
            {
              "code": 8,
              "name": "垃圾分类"
            },
            {
              "code": 9,
              "name": "污染排放"
            },
            {
              "code": 10,
              "name": "生态保护"
            },
            {
              "code": 11,
              "name": "其他情况"
            }
          ],
          "name": "美丽家乡"
        },
        {
          "code": 3,
          "subBizTypes": [
            {
              "code": 12,
              "name": "优惠活动"
            },
            {
              "code": 13,
              "name": "住房问题"
            },
            {
              "code": 14,
              "name": "饮水用电"
            },
            {
              "code": 15,
              "name": "交通问题"
            },
            {
              "code": 16,
              "name": "扶贫帮困"
            },
            {
              "code": 17,
              "name": "公共报修"
            },
            {
              "code": 29,
              "name": "积分申请"
            },
            {
              "code": 18,
              "name": "其他情况"
            }
          ],
          "name": "民生生活"
        },
        {
          "code": 4,
          "subBizTypes": [
            {
              "code": 19,
              "name": "好人好事"
            },
            {
              "code": 20,
              "name": "举报不良"
            },
            {
              "code": 21,
              "name": "风俗乡情"
            },
            {
              "code": 22,
              "name": "活动提议"
            },
            {
              "code": 23,
              "name": "人际协调"
            },
            {
              "code": 25,
              "name": "矛盾纠纷"
            },
            {
              "code": 24,
              "name": "其他情况"
            }
          ],
          "name": "文明乡风"
        },
        {
          "code": 5,
          "subBizTypes": [
            {
              "code": 26,
              "name": "包卫生"
            },
            {
              "code": 27,
              "name": "包绿化"
            },
            {
              "code": 28,
              "name": "包秩序"
            }
          ],
          "name": "门前三包"
        }
      ]
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取页面类型
    const type = options.type || 'report';

    // 从全局数据中获取详情数据
    const app = getApp();
    const detailData = app.globalData.detailData;

    if (detailData) {
      // 处理图片数组
      const replyImages = detailData.replyImages ? detailData.replyImages.split(';').filter(img => img.trim()) : [];
      const reportImages = detailData.reportImages ? detailData.reportImages.split(';').filter(img => img.trim()) : [];

      this.setData({
        detailData,
        type,
        replyImages,
        reportImages
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: detailData.reportTitle || '详情'
      });
    } else {
      wx.showToast({
        title: '数据加载失败',
        icon: 'none'
      });
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 预览图片
   */
  previewImage(e) {
    const current = e.currentTarget.dataset.src;
    const type = e.currentTarget.dataset.type;

    let urls = [];
    if (type === 'reply') {
      urls = this.data.replyImages;
    } else if (type === 'report') {
      urls = this.data.reportImages;
    }

    wx.previewImage({
      current,
      urls
    });
  },

  /**
   * 格式化状态文本
   */
  getStatusText(status) {
    const statusMap = {
      0: '待处理',
      1: '处理中',
      2: '已完成',
      3: '已关闭'
    };
    return statusMap[status] || '未知状态';
  }
})
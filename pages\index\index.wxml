<view class="page-container">

  <view class="header">
    <text># 随手拍</text>
    <view class="logo-container">
      <image class="logo-image" src="/images/logo.jpg" mode="aspectFit"></image>
    </view>
  </view>

  <view class="input-section">

    <textarea
      placeholder="请描述您需要反映的内容~"
      class="content-input"
      auto-height
      placeholder-class="placeholder-style"
      bindinput="onDescriptionInput" value="{{description}}"
    />
  </view>

  <!-- 图片选择区域 -->
  <view class="upload-section">
    <view class="image-list">
      <block wx:for="{{mediaList}}" wx:key="index">
        <view class="preview-item">
          <image wx:if="{{item.type === 'image'}}" src="{{item.path}}" mode="aspectFill" bindtap="previewMedia" data-index="{{index}}"></image>
          <video wx:if="{{item.type === 'video'}}" src="{{item.path}}" class="preview-video" bindtap="previewMedia" data-index="{{index}}"></video>
          <view class="delete-btn" catchtap="deleteMedia" data-index="{{index}}">×</view>
        </view>
      </block>

      <view class="upload-box" bindtap="chooseMedia" wx:if="{{tempImagePaths.length < 6}}">
        <view class="plus-icon">+</view>
        <text class="upload-tip">点击上传图片</text>
      </view>
    </view>
  </view>

  <view class="divider-line"></view>

  <view class="form-item" bindtap="chooseLocation">
    <text class="label">所在位置</text>
    <text class="selected-value" wx:if="{{location}}">{{location}}</text>
    <view class="right-arrow"></view>
  </view>

  <view class="form-item" bindtap="chooseCategory">
    <text class="label">事件分类</text>
    <text class="selected-value" wx:if="{{category && subCategory}}">{{category}} - {{subCategory}}</text>
    <view class="right-arrow"></view>
  </view>

  <view class="submit-button" bindtap="submitForm">提交</view>
</view>
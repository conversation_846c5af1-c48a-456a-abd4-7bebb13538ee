<!--pages/detail.wxml-->
<view class="detail-container" wx:if="{{detailData}}">
  <!-- 标题区域 -->
  <view class="detail-header">
    <view class="title">{{detailData.reportTitle}}</view>
    <view class="time">{{detailData.reportTime}}</view>
  </view>

  <!-- 基本信息 -->
  <view class="detail-section">
    <view class="section-title">基本信息</view>
    <view class="info-item">
      <text class="label">业务类型：</text>
      <text class="value">{{detailData.bizType}}</text>
    </view>
    <view class="info-item">
      <text class="label">子业务类型：</text>
      <text class="value">{{detailData.subBizType}}</text>
    </view>
    <view class="info-item">
      <text class="label">处理状态：</text>
      <text class="value status-{{detailData.reportStatus}}">
        {{detailData.reportStatus === 0 ? '待处理' : detailData.reportStatus === 1 ? '处理中' : detailData.reportStatus === 2 ? '已完成' : '已关闭'}}
      </text>
    </view>
    <view class="info-item" wx:if="{{detailData.score !== null}}">
      <text class="label">评分：</text>
      <text class="value">{{detailData.score}}</text>
    </view>
  </view>

  <!-- 上报图片 -->
  <view class="detail-section" wx:if="{{reportImages.length > 0}}">
    <view class="section-title">上报图片</view>
    <view class="image-grid">
      <image
        wx:for="{{reportImages}}"
        wx:key="index"
        src="{{item}}"
        class="grid-image"
        mode="aspectFill"
        bindtap="previewImage"
        data-src="{{item}}"
        data-type="report"
      />
    </view>
  </view>

  <!-- 回复内容 -->
  <view class="detail-section" wx:if="{{detailData.replyContent}}">
    <view class="section-title">回复内容</view>
    <view class="reply-content">{{detailData.replyContent}}</view>
  </view>

  <!-- 回复图片 -->
  <view class="detail-section" wx:if="{{replyImages.length > 0}}">
    <view class="section-title">回复图片</view>
    <view class="image-grid">
      <image
        wx:for="{{replyImages}}"
        wx:key="index"
        src="{{item}}"
        class="grid-image"
        mode="aspectFill"
        bindtap="previewImage"
        data-src="{{item}}"
        data-type="reply"
      />
    </view>
  </view>
</view>

<!-- 数据加载失败提示 -->
<view class="error-container" wx:else>
  <view class="error-text">数据加载失败</view>
</view>
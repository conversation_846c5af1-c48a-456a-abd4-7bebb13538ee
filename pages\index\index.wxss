.page-container {
  padding: 32rpx;
  background: #f5f9ff;
  min-height: 100vh;
}

/* Logo 样式 */
.logo-container {
  display: flex;
  justify-content: center;
  margin: 20rpx 0;
}

.logo-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.header {
  font-family: "黑体";
  font-size: 40rpx;
  color: #000;
  font-weight: bold;
  padding: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.input-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  margin: 20rpx 0;
}

.title-input {
  width: 100%;
  height: 80rpx;
  font-size: 34rpx;
  color: #333;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  padding-bottom: 10rpx;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
}

.placeholder-style {
  color: #999;
}

.divider-line {
  height: 2rpx;
  background: #e6e6e6;
  margin: 40rpx 0;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  margin: 20rpx 0;
}

.label {
  font-family: "黑体";
  font-size: 32rpx;
  color: #333;
}

.selected-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  max-width: 400rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.right-arrow {
  width: 20rpx;
  height: 20rpx;
  border-top: 4rpx solid #666;
  border-right: 4rpx solid #666;
  transform: rotate(45deg);
}

.submit-button {
  background: #1890ff;
  color: #fff;
  font-family: "黑体";
  font-size: 36rpx;
  text-align: center;
  padding: 32rpx;
  border-radius: 50rpx;
  margin: 60rpx 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}
/* 上传区域样式 */
.upload-section {
  margin: 30rpx 0;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.upload-box {
  width: 200rpx;
  height: 200rpx;
  background: #f0f4f9;
  border: 2rpx dashed #1890ff;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.plus-icon {
  font-size: 60rpx;
  color: #1890ff;
  line-height: 1;
  margin-bottom: 10rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  padding: 0 20rpx;
}

/* 预览区域样式 */
.preview-item {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

.preview-video {
  width: 100%;
  height: 100%;
}

.delete-btn {
  position: absolute;
  right: 10rpx;
  top: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  text-align: center;
  line-height: 40rpx;
  font-size: 36rpx;
}

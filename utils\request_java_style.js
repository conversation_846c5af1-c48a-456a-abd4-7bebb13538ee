// 微信小程序环境下模拟Java风格的签名生成
const CryptoJS = require('./crypto-js.min.js');

// 配置信息
const config = {
  baseUrl: 'http://36.133.208.76:8088', // 替换为您的实际API基础URL
  thirdKey: '60820f04-2eb',
  thirdSecret: 'A4AC796044AF1B441A96'
};

/**
 * 将CryptoJS的WordArray转换为字节数组
 * @param {WordArray} wordArray - CryptoJS的WordArray
 * @returns {Uint8Array} 字节数组
 */
function wordArrayToUint8Array(wordArray) {
  const words = wordArray.words;
  const sigBytes = wordArray.sigBytes;
  const u8 = new Uint8Array(sigBytes);

  for (let i = 0; i < sigBytes; i++) {
    const byte = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    u8[i] = byte;
  }

  return u8;
}

/**
 * 将字节数组转换为十六进制字符串
 * @param {Uint8Array} bytes - 字节数组
 * @returns {string} 十六进制字符串
 */
function bytesToHex(bytes) {
  return Array.from(bytes)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * SHA-256 哈希计算
 * @param {string} message - 消息
 * @returns {Uint8Array} 哈希结果字节数组
 */
function sha256(message) {
  // 使用 CryptoJS 计算 SHA-256 哈希
  const hash = CryptoJS.SHA256(message);

  // 将 WordArray 转换为 Uint8Array
  return wordArrayToUint8Array(hash);
}

/**
 * HMAC-SHA256 计算
 * @param {string|Uint8Array} key - 密钥
 * @param {string|Uint8Array} message - 消息
 * @returns {Uint8Array} HMAC 结果字节数组
 */
function hmacSha256(key, message) {
  // 如果密钥是 Uint8Array，转换为 WordArray
  let keyWordArray = key;
  if (key instanceof Uint8Array) {
    keyWordArray = CryptoJS.lib.WordArray.create(key);
  }

  // 如果消息是 Uint8Array，转换为 WordArray
  let messageWordArray = message;
  if (message instanceof Uint8Array) {
    messageWordArray = CryptoJS.lib.WordArray.create(message);
  }

  // 计算 HMAC
  const hmac = CryptoJS.HmacSHA256(messageWordArray, keyWordArray);

  // 将 WordArray 转换为 Uint8Array
  return wordArrayToUint8Array(hmac);
}

/**
 * 生成签名 - 与Java实现完全一致
 * @param {string} uri - 请求路径
 * @param {object|string} params - 请求参数
 * @returns {object} 包含签名和请求时间的对象
 */
function generateSign(uri, params) {
  const requestTime = Date.now().toString();

  // 将params转为字符串
  let paramsStr = '';
  if (typeof params === 'object') {
    paramsStr = JSON.stringify(params);
  } else if (typeof params === 'string') {
    paramsStr = params;
  }
  console.log('参数字符串:', paramsStr);

  try {
    // 第一步：根据时间戳做一次加密
    // Java代码：
    // String stringBuffer = thirdKey + "|" + requestTime + "|" + uri;
    // HmacUtils hmac = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, stringBuffer);
    // byte[] kDate = hmac.hmac(thirdSecret);
    const stringBuffer = `${config.thirdKey}|${requestTime}|${uri}`;
    console.log('StringBuffer:', stringBuffer);

    // 使用stringBuffer作为密钥，对thirdSecret进行HMAC-SHA256加密
    const kDate = hmacSha256(stringBuffer, config.thirdSecret);
    console.log('kDate (字节数组):', kDate);

    // 第二步：计算参数的SHA256哈希
    // Java代码：
    // byte[] messageType = DigestUtils.sha256(params);
    const messageType = sha256(paramsStr);
    console.log('messageType (字节数组):', messageType);

    // 第三步：使用kDate作为密钥，对messageType进行HMAC-SHA256加密
    // Java代码：
    // hmac = new HmacUtils(HmacAlgorithms.HMAC_SHA_256, kDate);
    // return hmac.hmacHex(messageType);
    const signatureBytes = hmacSha256(kDate, messageType);
    const signature = bytesToHex(signatureBytes);
    console.log('签名:', signature);

    return {
      signature,
      requestTime
    };
  } catch (error) {
    console.error('签名生成失败:', error);
    throw error;
  }
}

/**
 * 请求函数
 * @param {object} options - 请求选项
 * @returns {Promise} 请求Promise
 */
function request(options) {
  return new Promise((resolve, reject) => {
    try {
      const { url, method = 'GET', data = {}, header = {} } = options;

      // 获取相对路径URI
      const uri = url.replace(config.baseUrl, '');
      console.log('请求路径:', uri);
      console.log('请求数据:', data);

      // 生成签名和时间戳
      const { signature, requestTime } = generateSign(uri, data);

      // 构建请求头
      const requestHeader = {
        'Accept-ThirdKey': config.thirdKey,
        'Accept-Time': requestTime,
        'Accept-Sign': signature,
        'Content-Type': 'application/json',
        ...header
      };

      // 发起请求
      wx.request({
        url: url.startsWith('http') ? url : config.baseUrl + url,
        method,
        data,
        header: requestHeader,
        success: (res) => {
          // 可以在这里统一处理响应
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res.data);
          } else {
            // 处理错误状态码
            reject({
              code: res.statusCode,
              message: res.data.message || '请求失败',
              data: res.data
            });
          }
        },
        fail: (err) => {
          // 处理请求失败
          reject({
            code: -1,
            message: err.errMsg || '网络请求失败',
            data: err
          });
        }
      });
    } catch (error) {
      reject({
        code: -1,
        message: '请求处理失败',
        data: error
      });
    }
  });
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function get(url, data = {}, header = {}) {
  return request({
    url,
    method: 'GET',
    data,
    header
  });
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function post(url, data = {}, header = {}) {
  return request({
    url,
    method: 'POST',
    data,
    header
  });
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function put(url, data = {}, header = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    header
  });
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {object} data - 请求参数
 * @param {object} header - 额外的请求头
 * @returns {Promise} 请求Promise
 */
function del(url, data = {}, header = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    header
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  del,
  generateSign
};

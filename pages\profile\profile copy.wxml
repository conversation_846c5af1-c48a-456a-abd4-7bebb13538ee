<!--profile.wxml-->
<view class="profile-container">
  <!-- 用户信息区域 -->
  <view class="user-info-section">
    <view class="avatar-container">
      <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
    </view>
    <view class="user-name">
      <!--<block wx:if="{{!hasUserInfo}}">
        <button class="login-btn" bindtap="getUserProfile">点击登录</button>
      </block>
      <block wx:else>
        <text>{{userInfo.nickName}}</text>
      </block>-->
    </view>
  </view>

  <!-- 功能菜单列表 -->
  <view class="menu-list">
    <view class="menu-item" bindtap="viewPhotos">
      <view class="menu-item-left">
        <image class="menu-icon" src="/images/photos.png" mode="aspectFit"></image>
        <text>我的随手拍</text>
      </view>
      <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>

    <view class="menu-item" bindtap="goToSettings">
      <view class="menu-item-left">
        <image class="menu-icon" src="/images/settings.png" mode="aspectFit"></image>
        <text>我的积分</text>
      </view>
      <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>

    <view class="menu-item" bindtap="aboutUs">
      <view class="menu-item-left">
        <image class="menu-icon" src="/images/about.png" mode="aspectFit"></image>
        <text>退出登录</text>
      </view>
      <image class="arrow-icon" src="/images/arrow-right.png" mode="aspectFit"></image>
    </view>
  </view>
</view>
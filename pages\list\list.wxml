<!--list.wxml-->
<view class="containers">

  <view class="list-content">
    <block wx:if="{{listData.length > 0}}">
      <view class="list-item" wx:for="{{listData}}" wx:key="id" bindtap="viewDetail" data-index="{{index}}">
        <view class="item-main">
          <view class="item-title">{{item.reportTitle || '无标题'}}</view>
          <view class="item-title" wx:if="{{item.replyContent}}">回复：{{item.replyContent || '暂无回复内容'}}</view>
          <view class="item-time">{{item.reportTime || ''}}</view>
        </view>

        <!-- 根据类型显示不同的内容 -->
        <view class="item-extra">
          <block wx:if="{{type === 'report'}}">
            <view class="status-tag status-{{item.reportStatus}}">
              <text wx:if="{{item.reportStatus === 0}}">已提交</text>
              <text wx:if="{{item.reportStatus === 1}}">已回复</text>
              <text wx:if="{{item.reportStatus === 2}}">已完成</text>
            </view>
          </block>

          <block wx:if="{{type === 'score' && item.score && item.score > 0}}">
            <view class="score-tag">
              <text class="score-value">+{{item.score || 0}}</text>
              <text class="score-label">积分</text>
            </view>
          </block>
        </view>
      </view>
    </block>

    <view class="empty-list" wx:if="{{!loading && listData.length === 0}}">
      <text class="empty-text">暂无数据</text>
    </view>
  </view>
</view>
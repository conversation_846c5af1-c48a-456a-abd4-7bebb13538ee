// index.js
const api = require('../../api/api.js');

Page({
  data: {
    tempImagePaths: [],
    mediaList: [], // 用于存储图片和视频
    location: '',
    latitude: '',
    longitude: '',
    category: '',
    subCategory: '',
    categoryCode: '',
    subCategoryCode: '',
    title: '',
    description: '',
    listData: [], // 用于存储获取的列表数据
    categoryData: {
      "bizTypes": [
        {
          "code": 1,
          "subBizTypes": [
            {
              "code": 1,
              "name": "疫情"
            },
            {
              "code": 2,
              "name": "农灾"
            },
            {
              "code": 3,
              "name": "地震"
            },
            {
              "code": 25,
              "name": "火灾"
            },
            {
              "code": 4,
              "name": "洪水"
            },
            {
              "code": 5,
              "name": "其他"
            }
          ],
          "name": "疫情灾情"
        },
        {
          "code": 2,
          "subBizTypes": [
            {
              "code": 6,
              "name": "街道环境"
            },
            {
              "code": 7,
              "name": "家乡美景"
            },
            {
              "code": 8,
              "name": "垃圾分类"
            },
            {
              "code": 9,
              "name": "污染排放"
            },
            {
              "code": 10,
              "name": "生态保护"
            },
            {
              "code": 11,
              "name": "其他情况"
            }
          ],
          "name": "美丽家乡"
        },
        {
          "code": 3,
          "subBizTypes": [
            {
              "code": 12,
              "name": "优惠活动"
            },
            {
              "code": 13,
              "name": "住房问题"
            },
            {
              "code": 14,
              "name": "饮水用电"
            },
            {
              "code": 15,
              "name": "交通问题"
            },
            {
              "code": 16,
              "name": "扶贫帮困"
            },
            {
              "code": 17,
              "name": "公共报修"
            },
            {
              "code": 29,
              "name": "积分申请"
            },
            {
              "code": 18,
              "name": "其他情况"
            }
          ],
          "name": "民生生活"
        },
        {
          "code": 4,
          "subBizTypes": [
            {
              "code": 19,
              "name": "好人好事"
            },
            {
              "code": 20,
              "name": "举报不良"
            },
            {
              "code": 21,
              "name": "风俗乡情"
            },
            {
              "code": 22,
              "name": "活动提议"
            },
            {
              "code": 23,
              "name": "人际协调"
            },
            {
              "code": 25,
              "name": "矛盾纠纷"
            },
            {
              "code": 24,
              "name": "其他情况"
            }
          ],
          "name": "文明乡风"
        },
        {
          "code": 5,
          "subBizTypes": [
            {
              "code": 26,
              "name": "包卫生"
            },
            {
              "code": 27,
              "name": "包绿化"
            },
            {
              "code": 28,
              "name": "包秩序"
            }
          ],
          "name": "门前三包"
        }
      ]
    }
  },

  onLoad() {
    // 页面加载时的逻辑
  },



  // 处理标题输入
  onTitleInput(e) {
    this.setData({
      title: e.detail.value
    });
  },

  // 处理文本输入
  onDescriptionInput(e) {
    this.setData({
      description: e.detail.value
    });
  },

  // 选择媒体（图片或视频）
  chooseMedia() {
    this.chooseImage();
    // wx.showActionSheet({
    //   itemList: ['上传图片', '上传视频'],
    //   success: (res) => {
    //     if (res.tapIndex === 0) {
    //       // 选择图片
    //       this.chooseImage();
    //     } else if (res.tapIndex === 1) {
    //       // 选择视频
    //       this.chooseVideo();
    //     }
    //   }
    // });
  },

  // 将图片转换为base64格式
  imageToBase64(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: filePath,
        encoding: 'base64',
        success: res => {
          // 添加MIME类型前缀
          // 根据文件扩展名判断图片类型
          let mimePrefix = 'data:image/jpeg;base64,';
          if (filePath.toLowerCase().endsWith('.png')) {
            mimePrefix = 'data:image/png;base64,';
          } else if (filePath.toLowerCase().endsWith('.gif')) {
            mimePrefix = 'data:image/gif;base64,';
          } else if (filePath.toLowerCase().endsWith('.webp')) {
            mimePrefix = 'data:image/webp;base64,';
          }

          resolve(mimePrefix + res.data);
        },
        fail: err => {
          console.error('图片转base64失败:', err);
          reject(err);
        }
      });
    });
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 9 - this.data.mediaList.length, // 最多可以选择9个媒体文件
      sizeType: ['compressed'], // 压缩图
      sourceType: ['album', 'camera'], // 可以从相册选择或使用相机拍照
      success: (res) => {
        console.log(res);

        // 将新选择的图片添加到媒体列表
        const newImagePaths = res.tempFilePaths;
        const currentMediaList = this.data.mediaList;

        // 显示加载提示
        wx.showLoading({
          title: '处理图片中...',
        });

        // 将所有图片转换为base64格式
        const convertPromises = newImagePaths.map(path => this.imageToBase64(path));

        Promise.all(convertPromises)
          .then(base64DataArray => {
            // 将图片转换为媒体对象，包含base64数据
            const newMediaItems = newImagePaths.map((path, index) => ({
              type: 'image',
              path: path,
              base64: base64DataArray[index]
            }));

            // 合并媒体列表，确保不超过9个
            const allMediaItems = [...currentMediaList, ...newMediaItems];
            const finalMediaItems = allMediaItems.slice(0, 9);

            // 同时更新 tempImagePaths 以保持兼容性
            const imagePaths = finalMediaItems
              .filter(item => item.type === 'image')
              .map(item => item.path);

            this.setData({
              mediaList: finalMediaItems,
              tempImagePaths: imagePaths
            });

            console.log('媒体列表已更新，包含base64数据');
            console.log(this.data.mediaList);
            console.log(this.data.tempImagePaths);

            wx.hideLoading();
          })
          .catch(err => {
            console.error('处理图片失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '图片处理失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 将视频转换为base64格式
  videoToBase64(filePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: filePath,
        encoding: 'base64',
        success: res => {
          // 添加MIME类型前缀
          // 根据文件扩展名判断视频类型
          let mimePrefix = 'data:video/mp4;base64,';
          if (filePath.toLowerCase().endsWith('.avi')) {
            mimePrefix = 'data:video/x-msvideo;base64,';
          } else if (filePath.toLowerCase().endsWith('.mov')) {
            mimePrefix = 'data:video/quicktime;base64,';
          } else if (filePath.toLowerCase().endsWith('.webm')) {
            mimePrefix = 'data:video/webm;base64,';
          }

          resolve(mimePrefix + res.data);
        },
        fail: err => {
          console.error('视频转base64失败:', err);
          reject(err);
        }
      });
    });
  },

  // 选择视频
  chooseVideo() {
    wx.chooseVideo({
      sourceType: ['album', 'camera'], // 可以从相册选择或使用相机拍摄
      maxDuration: 60, // 最长60秒
      camera: 'back', // 默认使用后置摄像头
      compressed: true, // 压缩视频
      success: (res) => {
        // 将新选择的视频添加到媒体列表
        const currentMediaList = this.data.mediaList;

        // 检查是否已达到最大数量
        if (currentMediaList.length >= 9) {
          wx.showToast({
            title: '最多只能上传9个文件',
            icon: 'none'
          });
          return;
        }

        // 显示加载提示
        wx.showLoading({
          title: '处理视频中...',
        });

        // 将视频转换为base64格式
        this.videoToBase64(res.tempFilePath)
          .then(base64Data => {
            // 创建新的媒体对象，包含base64数据
            const newMediaItem = {
              type: 'video',
              path: res.tempFilePath,
              base64: base64Data,
              duration: res.duration,
              size: res.size,
              width: res.width,
              height: res.height
            };

            // 添加到媒体列表
            const newMediaList = [...currentMediaList, newMediaItem];

            // 同时更新 tempImagePaths 以保持兼容性
            const imagePaths = newMediaList
              .filter(item => item.type === 'image')
              .map(item => item.path);

            this.setData({
              mediaList: newMediaList,
              tempImagePaths: imagePaths
            });

            console.log('媒体列表已更新，包含视频base64数据');
            wx.hideLoading();
          })
          .catch(err => {
            console.error('处理视频失败:', err);
            wx.hideLoading();
            wx.showToast({
              title: '视频处理失败',
              icon: 'none'
            });
          });
      }
    });
  },

  // 预览媒体
  previewMedia(e) {
    const index = e.currentTarget.dataset.index;
    const media = this.data.mediaList[index];

    if (media.type === 'image') {
      // 预览图片
      const imagePaths = this.data.mediaList
        .filter(item => item.type === 'image')
        .map(item => item.path);

      wx.previewImage({
        current: media.path,
        urls: imagePaths
      });
    } else if (media.type === 'video') {
      // 预览视频
      wx.openVideoEditor({
        filePath: media.path,
        success: (res) => {
          console.log('视频编辑成功:', res);
        },
        fail: (err) => {
          console.log('视频编辑失败:', err);
          // 如果编辑器不可用，直接播放视频
          wx.navigateTo({
            url: `/pages/video-player/video-player?path=${encodeURIComponent(media.path)}`
          });
        }
      });
    }
  },

  // 删除媒体
  deleteMedia(e) {
    const index = e.currentTarget.dataset.index;
    let mediaList = this.data.mediaList;
    mediaList.splice(index, 1);

    // 同时更新 tempImagePaths 以保持兼容性
    const imagePaths = mediaList
      .filter(item => item.type === 'image')
      .map(item => item.path);

    this.setData({
      mediaList: mediaList,
      tempImagePaths: imagePaths
    });
  },

  // 选择位置
  chooseLocation() {
    wx.chooseLocation({
      success: (res) => {
        console.log(res);

        this.setData({
          location: res.address || res.name
        })
        this.setData({
          latitude: res.latitude
        })
        this.setData({
          longitude: res.longitude
        })
        wx.showToast({
          title: '位置已选择',
          icon: 'success'
        })
      }
    })
  },

  // 选择分类
  chooseCategory() {
    // 提取一级分类名称列表
    const mainCategories = this.data.categoryData.bizTypes.map(item => item.name);

    wx.showActionSheet({
      itemList: mainCategories,
      success: (res) => {
        const selectedIndex = res.tapIndex;
        const selectedCategory = this.data.categoryData.bizTypes[selectedIndex];

        // 设置选中的一级分类
        this.setData({
          category: selectedCategory.name,
          categoryCode: selectedCategory.code
        });

        // 显示二级分类选择
        this.chooseSubCategory(selectedCategory.subBizTypes);
      }
    });
  },

  // 选择二级分类
  chooseSubCategory(subCategories) {
    // 提取二级分类名称列表
    const subCategoryNames = subCategories.map(item => item.name);

    wx.showActionSheet({
      itemList: subCategoryNames,
      success: (res) => {
        const selectedSubIndex = res.tapIndex;
        const selectedSubCategory = subCategories[selectedSubIndex];

        // 设置选中的二级分类
        this.setData({
          subCategory: selectedSubCategory.name,
          subCategoryCode: selectedSubCategory.code
        });
      }
    });
  },

  // 提交表单
  submitForm() {

    if (!this.data.category || !this.data.subCategory) {
      wx.showToast({
        title: '请选择分类',
        icon: 'none'
      });
      return;
    }

    if (!this.data.description.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }
    if (!this.data.location.trim()) {
      wx.showToast({
        title: '请选择位置',
        icon: 'none'
      });
      return;
    }

    // 这里添加提交表单的逻辑
    wx.showLoading({
      title: '提交中...',
    });

    // 准备媒体数据
    const mediaData = this.data.mediaList.map(item => {
      return item.base64
        ;
    });
    let params = {
      branchId: '546991277420822528',
      phone: '18739317269',
      title: this.data.description,
      bizType: this.data.categoryCode,
      subBizType: this.data.subCategoryCode,
      content: this.data.description,
      location: this.data.location,
      latitude: this.data.latitude,
      longitude: this.data.longitude,
      images: mediaData
    }
    console.log(params);


    console.log('提交参数:', params);

    // 调用API提交数据
    api.report(params).then(response => {
      console.log('提交成功:', response);

      // 清空表单
      this.setData({
        tempImagePaths: [],
        mediaList: [],
        location: '',
        latitude: '',
        longitude: '',
        category: '',
        subCategory: '',
        categoryCode: '',
        subCategoryCode: '',
        title: '',
        description: '',
        doorFrontData: null
      });

      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
    }).catch(error => {
      console.error('提交失败:', error);
      wx.hideLoading();
      wx.showToast({
        title: '提交失败',
        icon: 'none'
      });
    });

  }
})





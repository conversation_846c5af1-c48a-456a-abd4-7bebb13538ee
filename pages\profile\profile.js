// profile.js
Page({
  data: {
    userInfo: {
      avatarUrl: 'https://mmbiz.qpic.cn/mmbiz/icTdbqWNOwNRna42FI242Lcia07jQodd2FJGIYQfG0LAJGFxM4FbnQP6yfMxBgJ0F3YRqJCJ1aPAK2dQagdusBZg/0',
      nickName: '微信用户'
    },
    hasUserInfo: false,
    canIUseGetUserProfile: wx.canIUse('getUserProfile')
  },

  onLoad() {
    // 检查是否已有用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      })
    }
  },

  getUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        })
        wx.setStorageSync('userInfo', res.userInfo)
      }
    })
  },

  // 跳转到我的积分页面
  goToSettings() {
    wx.navigateTo({
      url: '/pages/list/list?type=score'
    })
  },

  // 查看我的随手拍
  viewPhotos() {
    wx.navigateTo({
      url: '/pages/list/list?type=report'
    })
  },

  // 关于我们
  aboutUs() {
    wx.showToast({
      title: '退出登录成功',
      icon: 'none'
    })
  }
})
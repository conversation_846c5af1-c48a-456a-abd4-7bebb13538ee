/* pages/detail.wxss */

.detail-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标题区域 */
.detail-header {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.detail-header .title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.detail-header .time {
  font-size: 28rpx;
  color: #666;
}

/* 详情区块 */
.detail-section {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #e0e0e0;
}

/* 信息项 */
.info-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-size: 28rpx;
  color: #666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.info-item .value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

/* 状态样式 */
.status-0 {
  color: #ff9500;
}

.status-1 {
  color: #007aff;
}

.status-2 {
  color: #34c759;
}

.status-3 {
  color: #8e8e93;
}

/* 回复内容 */
.reply-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  word-break: break-all;
}

/* 图片网格 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.grid-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #f0f0f0;
}

/* 错误提示 */
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.error-text {
  font-size: 32rpx;
  color: #999;
}

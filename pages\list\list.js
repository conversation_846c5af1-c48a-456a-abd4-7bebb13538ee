// list.js
const api = require('../../api/api.js');

Page({
  data: {
    listData: [],
    type: '', // 'report' 表示随手拍列表, 'score' 表示积分列表
    loading: false,
    pageTitle: '列表'
  },

  onLoad(options) {
    // 根据传入的类型参数设置页面类型和标题
    const type = options.type || 'report';
    let pageTitle = '我的随手拍';

    if (type === 'score') {
      pageTitle = '我的积分';
    }

    this.setData({
      type,
      pageTitle
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: pageTitle
    });

    // 加载列表数据
    this.getListData();
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.getListData();
  },

  // 获取列表数据
  getListData() {
    this.setData({ loading: true });

    wx.showLoading({
      title: '加载中...',
    });

    // 根据不同类型调用不同的接口或传递不同的参数
    const params = {
      branchId: '546991277420822528',
      phone: '18739317269'
    };

    // 调用 getList 接口获取数据
    api.getList(params).then(res => {
      console.log('获取列表数据成功:', res);
      this.setData({
        listData: res.data.reportList || [],
        loading: false
      });
      wx.hideLoading();
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
    }).catch(err => {
      console.error('获取列表数据失败:', err);
      this.setData({ loading: false });
      wx.hideLoading();
      // 停止下拉刷新动画
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '获取数据失败',
        icon: 'none'
      });
    });
  },

  // 查看详情
  viewDetail(e) {
    const index = e.currentTarget.dataset.index;
    const item = this.data.listData[index];

    // 将完整的数据对象存储到全局数据中
    const app = getApp();
    app.globalData.detailData = item;

    // 跳转到详情页面
    wx.navigateTo({
      url: `/pages/detail/detail?type=${this.data.type}`
    });
  }
})
